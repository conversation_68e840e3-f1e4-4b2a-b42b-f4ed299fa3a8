use thiserror::Error;

#[derive(Error, Debug)]
pub enum MonitorError {
    #[error("Configuration error: {0}")]
    Config(String),

    #[error("RPC client error: {0}")]
    RpcClient(#[from] solana_client::client_error::ClientError),

    #[error("WebSocket connection error: {0}")]
    WebSocket(#[from] tokio_tungstenite::tungstenite::Error),

    #[error("JSON serialization/deserialization error: {0}")]
    Json(#[from] serde_json::Error),

    #[error("Borsh deserialization error: {0}")]
    Borsh(#[from] std::io::Error),

    #[error("Failed to parse string to Pubkey")]
    PubkeyParse,

    #[error("Transaction parsing failed: {0}")]
    TransactionParse(String),

    #[error("Required data not found in transaction: {0}")]
    DataNotFound(String),
}

pub type Result<T> = std::result::Result<T, MonitorError>;