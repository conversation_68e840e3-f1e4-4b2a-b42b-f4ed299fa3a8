//! # Error Handling
//!
//! This module defines the error types used throughout the pump.fun monitor service.
//! All errors implement the `E<PERSON>r` trait and provide detailed error messages for
//! debugging and logging purposes.

use thiserror::Error;

/// Comprehensive error type for all possible failures in the monitor service.
///
/// This enum covers all error scenarios that can occur during operation,
/// from configuration issues to network failures and data parsing errors.
/// Each variant includes relevant context and error chaining where appropriate.
#[derive(Error, Debug)]
pub enum MonitorError {
    /// Configuration-related errors (missing env vars, invalid values, etc.)
    #[error("Configuration error: {0}")]
    Config(String),

    /// Errors from Solana RPC client operations (network, rate limiting, etc.)
    #[error("RPC client error: {0}")]
    RpcClient(#[from] solana_client::client_error::ClientError),

    /// WebSocket connection and communication errors
    #[error("WebSocket connection error: {0}")]
    WebSocket(#[from] tokio_tungstenite::tungstenite::Error),

    /// JSON serialization/deserialization errors
    #[error("JSON serialization/deserialization error: {0}")]
    Json(#[from] serde_json::Error),

    /// Borsh deserialization errors (for Solana account data)
    #[error("Borsh deserialization error: {0}")]
    Borsh(#[from] std::io::Error),

    /// Failed to parse a string as a Solana public key
    #[error("Failed to parse string to Pubkey")]
    PubkeyParse,

    /// Transaction parsing and validation errors
    #[error("Transaction parsing failed: {0}")]
    TransactionParse(String),

    /// Missing required data in transactions or accounts
    #[error("Required data not found in transaction: {0}")]
    DataNotFound(String),
}

/// Convenience type alias for Results using our custom error type.
///
/// This allows for cleaner function signatures throughout the codebase
/// by avoiding repetition of the full `std::result::Result<T, MonitorError>` type.
pub type Result<T> = std::result::Result<T, MonitorError>;