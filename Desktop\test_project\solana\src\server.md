use crate::error::{Result};
use crate::types::{TokenCreationEvent, WebSocketMessage};
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, RwLock};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use uuid::Uuid;

/// Client connection information
#[derive(Debug, Clone)]
pub struct ClientConnection {
    pub id: String,
    pub addr: SocketAddr,
    pub sender: tokio::sync::mpsc::UnboundedSender<Message>,
}

/// WebSocket server for broadcasting token events to clients
pub struct WebSocketServer {
    port: u16,
    event_receiver: broadcast::Receiver<TokenCreationEvent>,
    clients: Arc<RwLock<HashMap<String, ClientConnection>>>,
}

impl WebSocketServer {
    pub fn new(port: u16, event_receiver: broadcast::Receiver<TokenCreationEvent>) -> Self {
        Self {
            port,
            event_receiver,
            clients: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Start the WebSocket server
    pub async fn start(mut self) -> Result<()> {
        let addr = format!("0.0.0.0:{}", self.port);
        let listener = TcpListener::bind(&addr).await?;

        info!("WebSocket server listening on {}", addr);

        // Clone clients and event receiver for the event broadcaster
        let clients_for_broadcast = Arc::clone(&self.clients);
        let mut event_receiver = self.event_receiver;

        // Start event broadcaster task
        tokio::spawn(async move {
            Self::broadcast_events_static(event_receiver, clients_for_broadcast).await;
        });

        // Accept incoming connections
        while let Ok((stream, addr)) = listener.accept().await {
            let clients = Arc::clone(&self.clients);

            tokio::spawn(async move {
                if let Err(e) = Self::handle_connection(stream, addr, clients).await {
                    error!("Connection error for {}: {}", addr, e);
                }
            });
        }

        Ok(())
    }

    /// Handle individual client connection
    async fn handle_connection(
        stream: TcpStream,
        addr: SocketAddr,
        clients: Arc<RwLock<HashMap<String, ClientConnection>>>,
    ) -> Result<()> {
        info!("New client connected: {}", addr);

        let ws_stream = accept_async(stream).await?;
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        // Generate unique client ID
        let client_id = Uuid::new_v4().to_string();
        
        // Create message channel for this client
        let (msg_sender, mut msg_receiver) = tokio::sync::mpsc::unbounded_channel();

        // Store client connection
        {
            let mut clients_guard = clients.write().await;
            clients_guard.insert(
                client_id.clone(),
                ClientConnection {
                    id: client_id.clone(),
                    addr,
                    sender: msg_sender,
                },
            );
        }

        // Send welcome message
        let welcome_msg = WebSocketMessage::Connected {
            client_id: client_id.clone(),
        };
        
        if let Ok(welcome_json) = serde_json::to_string(&welcome_msg) {
            if let Err(e) = ws_sender.send(Message::Text(welcome_json)).await {
                warn!("Failed to send welcome message to {}: {}", addr, e);
            }
        }

        info!("Client {} registered successfully", client_id);

        // Handle incoming and outgoing messages
        loop {
            tokio::select! {
                // Handle incoming messages from client
                msg = ws_receiver.next() => {
                    match msg {
                        Some(Ok(Message::Text(text))) => {
                            debug!("Received from client {}: {}", client_id, text);
                            
                            // Handle client messages (ping, filters, etc.)
                            if let Err(e) = Self::handle_client_message(&text, &mut ws_sender).await {
                                warn!("Error handling client message: {}", e);
                            }
                        }
                        Some(Ok(Message::Ping(data))) => {
                            if let Err(e) = ws_sender.send(Message::Pong(data)).await {
                                warn!("Failed to send pong to client {}: {}", client_id, e);
                                break;
                            }
                        }
                        Some(Ok(Message::Close(_))) => {
                            info!("Client {} disconnected", client_id);
                            break;
                        }
                        Some(Err(e)) => {
                            error!("WebSocket error for client {}: {}", client_id, e);
                            break;
                        }
                        None => {
                            warn!("WebSocket stream ended for client {}", client_id);
                            break;
                        }
                        _ => {}
                    }
                }
                // Handle outgoing messages to client
                msg = msg_receiver.recv() => {
                    match msg {
                        Some(message) => {
                            if let Err(e) = ws_sender.send(message).await {
                                error!("Failed to send message to client {}: {}", client_id, e);
                                break;
                            }
                        }
                        None => {
                            debug!("Message channel closed for client {}", client_id);
                            break;
                        }
                    }
                }
            }
        }

        // Remove client from active connections
        {
            let mut clients_guard = clients.write().await;
            clients_guard.remove(&client_id);
        }

        info!("Client {} disconnected and cleaned up", client_id);
        Ok(())
    }

    /// Handle messages from clients (ping, filters, etc.)
    async fn handle_client_message(
        message: &str,
        ws_sender: &mut futures_util::stream::SplitSink<
            tokio_tungstenite::WebSocketStream<TcpStream>,
            Message,
        >,
    ) -> Result<()> {
        // Try to parse client message
        if let Ok(client_msg) = serde_json::from_str::<serde_json::Value>(message) {
            match client_msg.get("type").and_then(|t| t.as_str()) {
                Some("ping") => {
                    let pong_msg = WebSocketMessage::Pong;
                    let pong_json = serde_json::to_string(&pong_msg)?;
                    ws_sender.send(Message::Text(pong_json)).await?;
                }
                Some("subscribe") => {
                    // Handle subscription requests (filtering, etc.)
                    debug!("Client subscription request: {}", message);
                }
                _ => {
                    debug!("Unknown client message type: {}", message);
                }
            }
        }

        Ok(())
    }

    /// Broadcast token events to all connected clients (static version)
    async fn broadcast_events_static(
        mut event_receiver: broadcast::Receiver<TokenCreationEvent>,
        clients: Arc<RwLock<HashMap<String, ClientConnection>>>,
    ) {
        info!("Event broadcaster started");

        while let Ok(event) = event_receiver.recv().await {
            let message = WebSocketMessage::TokenCreated(event.clone());
            
            match serde_json::to_string(&message) {
                Ok(json_message) => {
                    let ws_message = Message::Text(json_message);
                    let clients_guard = clients.read().await;
                    
                    // Send to all connected clients
                    let mut disconnected_clients = Vec::new();
                    
                    for (client_id, client) in clients_guard.iter() {
                        if let Err(_) = client.sender.send(ws_message.clone()) {
                            // Client channel is closed, mark for removal
                            disconnected_clients.push(client_id.clone());
                        }
                    }
                    
                    drop(clients_guard);
                    
                    // Remove disconnected clients
                    if !disconnected_clients.is_empty() {
                        let mut clients_guard = clients.write().await;
                        for client_id in disconnected_clients {
                            clients_guard.remove(&client_id);
                            debug!("Removed disconnected client: {}", client_id);
                        }
                    }
                    
                    debug!("Broadcasted token event to {} clients", clients.read().await.len());
                }
                Err(e) => {
                    error!("Failed to serialize event: {}", e);
                }
            }
        }

        warn!("Event broadcaster stopped");
    }

    /// Get current client count
    pub async fn get_client_count(&self) -> usize {
        self.clients.read().await.len()
    }

    /// Get connected client information
    pub async fn get_clients(&self) -> Vec<ClientConnection> {
        self.clients.read().await.values().cloned().collect()
    }
}