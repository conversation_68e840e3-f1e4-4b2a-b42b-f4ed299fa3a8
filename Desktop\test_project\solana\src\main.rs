//! # Pump.fun Token Monitor
//!
//! A real-time monitoring service for pump.fun token creation events on Solana.
//!
//! This service connects to Solana's RPC WebSocket, monitors the pump.fun program
//! for token creation transactions, and broadcasts structured events to connected
//! WebSocket clients in real-time.
//!
//! ## Architecture
//!
//! The service consists of two main components running concurrently:
//! - **RPC Monitor**: Connects to Solana WebSocket and processes transactions
//! - **WebSocket Server**: Serves clients and broadcasts token creation events
//!
//! ## Configuration
//!
//! The service is configured via environment variables:
//! - `SOLANA_RPC_HTTP_URL`: Solana HTTP RPC endpoint
//! - `SOLANA_RPC_WSS_URL`: Solana WebSocket RPC endpoint
//! - `WEBSOCKET_SERVER_PORT`: Port for the WebSocket server
//! - `PUMP_FUN_PROGRAM_ID`: Pump.fun program address (default: 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P)

mod data_models;
mod error;
mod rpc_client;
mod websocket_server;

use dotenv::dotenv;
use log::info;
use rpc_client::SolanaRpcMonitor;
use std::env;
use tokio::sync::broadcast;

/// Main entry point for the pump.fun token monitor service.
///
/// This function:
/// 1. Loads configuration from environment variables
/// 2. Sets up logging
/// 3. Creates a broadcast channel for token events
/// 4. Spawns the RPC monitor and WebSocket server tasks
/// 5. Runs both tasks concurrently until one exits
#[tokio::main]
async fn main() {
    // Load environment variables from .env file
    dotenv().ok();

    // Initialize logging with INFO level by default
    env_logger::init_from_env(env_logger::Env::default().default_filter_or("info"));

    info!("Starting pump.fun monitor service...");

    // Load configuration from environment variables
    let http_url = env::var("SOLANA_RPC_HTTP_URL").expect("SOLANA_RPC_HTTP_URL must be set");
    let wss_url = env::var("SOLANA_RPC_WSS_URL").expect("SOLANA_RPC_WSS_URL must be set");
    let pump_fun_id = env::var("PUMP_FUN_PROGRAM_ID").expect("PUMP_FUN_PROGRAM_ID must be set");
    let ws_port = env::var("WEBSOCKET_SERVER_PORT")
        .expect("WEBSOCKET_SERVER_PORT must be set")
        .parse::<u16>()
        .expect("Invalid WebSocket port number");

    // Create a broadcast channel for token creation events
    // Buffer size of 100 events - adjust based on expected throughput
    let (tx, rx) = broadcast::channel(100);

    // Initialize the Solana RPC monitor
    let monitor = SolanaRpcMonitor::new(http_url, wss_url, pump_fun_id, tx)
        .expect("Failed to create Solana Monitor");

    // Spawn the RPC monitor task
    let monitor_handle = tokio::spawn(async move {
        monitor.start().await;
    });

    // Spawn the WebSocket server task
    let server_handle =
        tokio::spawn(async move { websocket_server::run_server(ws_port, rx).await });

    // Run both tasks concurrently until one exits
    tokio::select! {
        _ = monitor_handle => info!("Solana RPC monitor task exited."),
        _ = server_handle => info!("WebSocket server task exited."),
    }
}