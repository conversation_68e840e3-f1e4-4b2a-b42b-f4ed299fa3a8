//! WebSocket server module for broadcasting pump.fun token creation events.
//!
//! This module provides a WebSocket server that accepts client connections and broadcasts
//! token creation events with support for per-client filtering. Each client can set custom
//! filter criteria to receive only the events they're interested in.
//!
//! # Features
//! - Multiple concurrent client connections
//! - Per-client filtering with real-time updates
//! - Automatic client cleanup on disconnection
//! - Thread-safe event broadcasting
//! - Comprehensive error handling
//!
//! # Architecture
//! The server maintains a list of connected clients, each with their own filter criteria.
//! When a token creation event is received, it's checked against each client's filter
//! and only sent to clients where the event matches their criteria.

use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, Mutex};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use futures_util::{SinkExt, StreamExt};
use log::{info, warn, error};
use serde_json;

use crate::data_models::{TokenCreatedEvent, FilterCriteria, ClientMessage};

/// Type alias for the client message sender
type ClientTx = tokio::sync::mpsc::UnboundedSender<Message>;

/// Represents a connected WebSocket client with individual state and filtering.
///
/// Each client maintains its own connection state and filter criteria,
/// allowing for personalized event streaming based on their preferences.
struct Client {
    /// The client's socket address for identification
    addr: SocketAddr,
    /// Message sender channel for this client
    tx: ClientTx,
    /// Thread-safe filter criteria that can be updated dynamically
    filter: Arc<Mutex<FilterCriteria>>,
}

/// Starts the WebSocket server and handles client connections.
///
/// This function creates a TCP listener on the specified address and spawns tasks
/// to handle incoming client connections. It also listens for token creation events
/// from the broadcast channel and distributes them to connected clients based on
/// their filter criteria.
///
/// # Arguments
/// * `addr` - The address to bind the server to (e.g., "127.0.0.1:8080")
/// * `mut event_receiver` - Broadcast receiver for token creation events
///
/// # Returns
/// * `Result<(), Box<dyn std::error::Error>>` - Ok if server starts successfully
pub async fn start_websocket_server(
    addr: &str,
    mut event_receiver: broadcast::Receiver<TokenCreatedEvent>,
) -> Result<(), Box<dyn std::error::Error>> {
    let listener = TcpListener::bind(addr).await?;
    info!("🚀 WebSocket server listening on {}", addr);

    // Shared list of connected clients with their filters
    let clients: Arc<Mutex<Vec<Arc<Client>>>> = Arc::new(Mutex::new(Vec::new()));
    let broadcast_clients = Arc::clone(&clients);

    // Spawn task to handle broadcasting events to clients
    tokio::spawn(async move {
        loop {
            match event_receiver.recv().await {
                Ok(event) => {
                    let mut dead_clients = Vec::new();
                    let locked_clients = broadcast_clients.lock().await;

                    for client in locked_clients.iter() {
                        // Check if event matches client's filter
                        let filter = client.filter.lock().await;
                        if matches_filter(&event, &filter) {
                            let event_json = serde_json::to_string(&event).unwrap();
                            let message = Message::Text(event_json);
                            
                            if let Err(_) = client.tx.send(message) {
                                dead_clients.push(client.addr);
                            }
                        }
                    }

                    // Remove dead clients outside the loop to avoid borrowing issues
                    drop(locked_clients);
                    if !dead_clients.is_empty() {
                        let mut locked_clients = broadcast_clients.lock().await;
                        locked_clients.retain(|client| !dead_clients.contains(&client.addr));
                        for addr in dead_clients {
                            info!("Removed dead client: {}", addr);
                        }
                    }
                }
                Err(broadcast::error::RecvError::Lagged(skipped)) => {
                    warn!("WebSocket broadcast lagged, skipped {} events", skipped);
                }
                Err(broadcast::error::RecvError::Closed) => {
                    error!("Event broadcast channel closed");
                    break;
                }
            }
        }
    });

    // Accept incoming connections
    while let Ok((stream, addr)) = listener.accept().await {
        let clients_clone = Arc::clone(&clients);
        tokio::spawn(handle_connection(stream, addr, clients_clone));
    }

    Ok(())
}

/// Handles a single WebSocket client connection.
///
/// This function upgrades the TCP connection to WebSocket, manages the client's
/// message sending channel, and processes incoming messages for filter updates.
///
/// # Arguments
/// * `stream` - The TCP stream for the client connection
/// * `addr` - The client's socket address
/// * `clients` - Shared list of connected clients
async fn handle_connection(
    stream: TcpStream,
    addr: SocketAddr,
    clients: Arc<Mutex<Vec<Arc<Client>>>>,
) {
    info!("New client connected: {}", addr);

    let ws_stream = match accept_async(stream).await {
        Ok(ws) => ws,
        Err(e) => {
            error!("Failed to accept WebSocket connection from {}: {}", addr, e);
            return;
        }
    };

    let (mut ws_sender, mut ws_receiver) = ws_stream.split();
    let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();

    // Create client with default (empty) filter
    let client = Arc::new(Client {
        addr,
        tx,
        filter: Arc::new(Mutex::new(FilterCriteria::default())),
    });

    // Add client to the list
    clients.lock().await.push(Arc::clone(&client));

    // Spawn task to send messages to this client
    let client_for_sender = Arc::clone(&client);
    tokio::spawn(async move {
        while let Some(message) = rx.recv().await {
            if let Err(e) = ws_sender.send(message).await {
                error!("Failed to send message to {}: {}", client_for_sender.addr, e);
                break;
            }
        }
    });

    // Handle incoming messages from this client
    while let Some(msg) = ws_receiver.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                // Try to parse as a client message
                match serde_json::from_str::<ClientMessage>(&text) {
                    Ok(ClientMessage::SetFilter { filter }) => {
                        let mut client_filter = client.filter.lock().await;
                        *client_filter = filter.clone();
                        info!("Updated filter for client {}: {:?}", addr, filter);
                    }
                    Err(e) => {
                        warn!("Invalid message from client {}: {} (error: {})", addr, text, e);
                    }
                }
            }
            Ok(Message::Close(_)) => {
                info!("Client {} sent close message", addr);
                break;
            }
            Err(e) => {
                error!("WebSocket error for client {}: {}", addr, e);
                break;
            }
            _ => {
                // Ignore other message types (binary, ping, pong)
            }
        }
    }

    info!("Client {} disconnected", addr);
    // Remove the client from the broadcast list
    clients.lock().await.retain(|client| client.addr != addr);
}

/// Checks if a token creation event matches the specified filter criteria.
///
/// This function applies client-specific filtering logic to determine whether
/// an event should be sent to a particular client. All specified filter criteria
/// must match for the event to pass (AND logic).
///
/// # Arguments
/// * `event` - The token creation event to check
/// * `filter` - The filter criteria to apply
///
/// # Returns
/// * `true` if the event matches all specified filter criteria
/// * `false` if any filter criterion fails to match
///
/// # Filter Logic
/// - `creator`: Exact string match (case-sensitive)
/// - `symbol`: Exact string match (case-insensitive)
/// - `name_contains`: Partial string match (case-insensitive)
fn matches_filter(event: &TokenCreatedEvent, filter: &FilterCriteria) -> bool {
    // Check creator filter (exact match, case-sensitive)
    if let Some(creator_filter) = &filter.creator {
        if &event.token.creator != creator_filter {
            return false;
        }
    }
    
    // Check symbol filter (exact match, case-insensitive)
    if let Some(symbol_filter) = &filter.symbol {
        if event.token.symbol.to_uppercase() != symbol_filter.to_uppercase() {
            return false;
        }
    }
    
    // Check name contains filter (partial match, case-insensitive)
    if let Some(name_filter) = &filter.name_contains {
        if !event.token.name.to_uppercase().contains(&name_filter.to_uppercase()) {
            return false;
        }
    }
    
    // If we passed all checks, it's a match
    true
}

#[cfg(test)]
mod tests;
