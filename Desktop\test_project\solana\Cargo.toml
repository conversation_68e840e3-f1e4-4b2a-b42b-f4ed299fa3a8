[package]
name = "pump_fun_monitor_corrected"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1", features = ["full"] }
tokio-tungstenite = { version = "0.23", features = ["native-tls"] }
futures-util = "0.3"
solana-client = "1.18"
solana-sdk = "1.18"
solana-transaction-status = "1.18"
solana-program = "1.18"
spl-token = "4.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
dotenv = "0.15"
log = "0.4"
env_logger = "0.11"
url = "2.5"
bs58 = "0.5"
borsh = "1.5"
thiserror = "1.0"
chrono = { version = "0.4", features = ["serde"] }