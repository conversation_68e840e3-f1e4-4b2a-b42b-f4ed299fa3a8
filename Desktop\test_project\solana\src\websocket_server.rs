//! # WebSocket Server
//!
//! This module implements the WebSocket server that broadcasts token creation events
//! to connected clients in real-time. It handles multiple concurrent client connections
//! and automatically removes disconnected clients.

use crate::data_models::{ClientMessage, FilterCriteria, TokenCreatedEvent};
use futures_util::{stream::StreamExt, SinkExt};
use log::{info, warn};
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, Mutex};
use tokio_tungstenite::accept_async;
use tokio_tungstenite::tungstenite::protocol::Message;

/// Type alias for the sender side of client communication channels.
/// Each connected client gets an unbounded channel for sending messages.
type ClientTx = tokio::sync::mpsc::UnboundedSender<Message>;

/// Represents a connected WebSocket client with individual state and filtering.
///
/// Each client maintains its own connection state and filter criteria,
/// allowing for personalized event streaming based on their preferences.
struct Client {
    /// The client's socket address for identification
    addr: SocketAddr,
    /// Message sender channel for this client
    tx: ClientTx,
    /// Thread-safe filter criteria that can be updated dynamically
    filter: Arc<Mutex<FilterCriteria>>,
}
/// Runs the WebSocket server that broadcasts token creation events to clients.
///
/// This function:
/// 1. Binds to the specified port and starts listening for connections
/// 2. Spawns a background task to broadcast events from the event_source to all clients
/// 3. Accepts new client connections and spawns handlers for each
/// 4. Automatically removes disconnected clients from the broadcast list
///
/// # Arguments
/// * `port` - The port number to bind the WebSocket server to
/// * `event_source` - Broadcast receiver for token creation events
///
/// # Panics
/// Panics if unable to bind to the specified port
pub async fn run_server(
    port: u16,
    mut event_source: broadcast::Receiver<TokenCreatedEvent>,
) {
    let addr = format!("127.0.0.1:{}", port);
    let listener = TcpListener::bind(&addr).await.expect("Can't listen");
    info!("WebSocket server listening on: ws://{}", addr);

    // Shared list of connected clients with their communication channels
    let clients: Arc<Mutex<Vec<Arc<Client>>>> = Arc::new(Mutex::new(Vec::new()));

    // Spawn background task to broadcast events to all connected clients
    let broadcast_clients = clients.clone();
    tokio::spawn(async move {
        loop {
            match event_source.recv().await {
                Ok(event) => {
                    // Serialize the event to JSON for transmission
                    //let event_json = serde_json::to_string(&event).unwrap();
                    //let message = Message::Text(event_json);
                    let mut dead_clients = Vec::new();
                    let mut locked_clients = broadcast_clients.lock().await;


                    for client in locked_clients.iter() {
                        let client_filter = client.filter.lock().await;
                        // Check if the event matches this client's filter
                        if matches_filter(&event, &client_filter) {
                            // Only send if it matches
                            let event_json = serde_json::to_string(&event).unwrap();
                            if client.tx.send(Message::Text(event_json)).is_err() {
                                dead_clients.push(client.addr);
                            }
                        }
                    }


                    if !dead_clients.is_empty() {
                        drop(locked_clients); // Drop lock before acquiring a new one
                        let mut clients_for_removal = broadcast_clients.lock().await;
                        clients_for_removal.retain(|c| !dead_clients.contains(&c.addr));
                        warn!("Removed {} dead client(s)", dead_clients.len());
                    }
                }
                Err(broadcast::error::RecvError::Lagged(n)) => {
                    warn!("Event broadcaster lagged, skipped {} messages", n);
                }
                Err(broadcast::error::RecvError::Closed) => {
                    info!("Event source closed, shutting down broadcaster");
                    break;
                }
            }
        }
    });

    // Main loop to accept new client connections
    while let Ok((stream, addr)) = listener.accept().await {
        let clients_clone = clients.clone();
        tokio::spawn(handle_connection(stream, addr, clients_clone));
    }
}

/// Handles an individual WebSocket client connection.
///
/// This function:
/// 1. Upgrades the TCP stream to a WebSocket connection
/// 2. Creates a communication channel for this client
/// 3. Adds the client to the broadcast list
/// 4. Spawns tasks to handle bidirectional communication
/// 5. Cleans up when the client disconnects
///
/// # Arguments
/// * `stream` - The TCP stream from the accepted connection
/// * `addr` - The client's socket address for identification
/// * `clients` - Shared list of connected clients
async fn handle_connection(
    stream: TcpStream,
    addr: SocketAddr,
    clients: Arc<Mutex<Vec<Arc<Client>>>>,
) {
    info!("New WebSocket connection from: {}", addr);

    // Upgrade TCP connection to WebSocket
    let ws_stream = match accept_async(stream).await {
        Ok(ws) => ws,
        Err(e) => {
            warn!("Failed to accept WebSocket connection from {}: {}", addr, e);
            return;
        }
    };

    let (mut ws_sender, mut ws_receiver) = ws_stream.split();
    let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();


    // Create the new client state with a default (empty) filter
    let client = Arc::new(Client {
        addr,
        tx,
        filter: Arc::new(Mutex::new(FilterCriteria::default())),
    });

    clients.lock().await.push(client.clone());


    // Task to forward broadcast messages to this specific client
    let forward_task = tokio::spawn(async move {
        while let Some(message) = rx.recv().await {
            if ws_sender.send(message).await.is_err() {
                // Client disconnected, exit the task
                break;
            }
        }
    });


    // Handle incoming messages from the client (for filtering)
    let receive_task = tokio::spawn(async move {
        while let Some(Ok(msg)) = ws_receiver.next().await {
            match msg {
                Message::Text(text) => {
                    // Try to parse the message as a ClientMessage
                    match serde_json::from_str::<ClientMessage>(&text) {
                        Ok(ClientMessage::SetFilter { filter }) => {
                            info!("Setting filter for client {}: {:?}", addr, filter);
                            let mut client_filter = client.filter.lock().await;
                            *client_filter = filter; // Update this client's filter
                        }
                        Err(_) => {
                            warn!("Received invalid message from {}: {}", addr, text);
                        }
                    }
                }
                Message::Close(_) => break,
                _ => {}
            }
        }
    });

    // Wait for either task to complete (client disconnect or error)
    tokio::select! {
        _ = forward_task => {},
        _ = receive_task => {},
    }

    info!("Client {} disconnected", addr);
    // Remove the client from the broadcast list
    clients.lock().await.retain(|client| client.addr != addr);
}


/// Checks if a token creation event matches the specified filter criteria.
///
/// This function applies client-specific filtering logic to determine whether
/// an event should be sent to a particular client. All specified filter criteria
/// must match for the event to pass (AND logic).
///
/// # Arguments
/// * `event` - The token creation event to check
/// * `filter` - The filter criteria to apply
///
/// # Returns
/// * `true` if the event matches all specified filter criteria
/// * `false` if any filter criterion fails to match
///
/// # Filter Logic
/// - `creator`: Exact string match (case-sensitive)
/// - `symbol`: Exact string match (case-insensitive)
/// - `name_contains`: Partial string match (case-insensitive)
fn matches_filter(event: &TokenCreatedEvent, filter: &FilterCriteria) -> bool {
    // Check creator filter (exact match, case-sensitive)
    if let Some(creator_filter) = &filter.creator {
        if &event.token.creator != creator_filter {
            return false;
        }
    }

    // Check symbol filter (exact match, case-insensitive)
    if let Some(symbol_filter) = &filter.symbol {
        if event.token.symbol.to_uppercase() != symbol_filter.to_uppercase() {
            return false;
        }
    }

    // Check name contains filter (partial match, case-insensitive)
    if let Some(name_filter) = &filter.name_contains {
        if !event.token.name.to_uppercase().contains(&name_filter.to_uppercase()) {
            return false;
        }
    }

    // If we passed all checks, it's a match
    true
}





