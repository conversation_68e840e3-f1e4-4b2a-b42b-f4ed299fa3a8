use crate::data_models::TokenCreatedEvent;
use futures_util::{stream::StreamExt, SinkExt};
use log::{info, warn};
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, Mutex};
use tokio_tungstenite::accept_async;
use tokio_tungstenite::tungstenite::protocol::Message;

type ClientTx = tokio::sync::mpsc::UnboundedSender<Message>;

pub async fn run_server(
    port: u16,
    mut event_source: broadcast::Receiver<TokenCreatedEvent>,
) {
    let addr = format!("127.0.0.1:{}", port);
    let listener = TcpListener::bind(&addr).await.expect("Can't listen");
    info!("WebSocket server listening on: ws://{}", addr);

    let clients: Arc<Mutex<Vec<(SocketAddr, ClientTx)>>> = Arc::new(Mutex::new(Vec::new()));

    // Task to broadcast events to all connected clients
    let broadcast_clients = clients.clone();
    tokio::spawn(async move {
        loop {
            match event_source.recv().await {
                Ok(event) => {
                    let event_json = serde_json::to_string(&event).unwrap();
                    let message = Message::Text(event_json);
                    let mut dead_clients = Vec::new();
                    let mut locked_clients = broadcast_clients.lock().await;

                    for (addr, tx) in locked_clients.iter() {
                        if tx.send(message.clone()).is_err() {
                            dead_clients.push(*addr);
                        }
                    }

                    if !dead_clients.is_empty() {
                        locked_clients.retain(|(addr, _)| !dead_clients.contains(addr));
                        warn!("Removed {} dead client(s)", dead_clients.len());
                    }
                }
                Err(broadcast::error::RecvError::Lagged(n)) => {
                    warn!("Event broadcaster lagged, skipped {} messages", n);
                }
                Err(broadcast::error::RecvError::Closed) => {
                    break;
                }
            }
        }
    });

    // Main loop to accept new connections
    while let Ok((stream, addr)) = listener.accept().await {
        let clients_clone = clients.clone();
        tokio::spawn(handle_connection(stream, addr, clients_clone));
    }
}

async fn handle_connection(
    stream: TcpStream,
    addr: SocketAddr,
    clients: Arc<Mutex<Vec<(SocketAddr, ClientTx)>>>,
) {
    info!("New WebSocket connection from: {}", addr);
    let ws_stream = match accept_async(stream).await {
        Ok(ws) => ws,
        Err(e) => {
            warn!("Failed to accept WebSocket connection from {}: {}", addr, e);
            return;
        }
    };

    let (mut ws_sender, mut ws_receiver) = ws_stream.split();
    let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();

    clients.lock().await.push((addr, tx));

    // Forward messages from the broadcast channel to this specific client
    let forward_task = tokio::spawn(async move {
        while let Some(message) = rx.recv().await {
            if ws_sender.send(message).await.is_err() {
                break;
            }
        }
    });

    // Handle incoming messages (like close frames)
    let receive_task = tokio::spawn(async move {
        while let Some(Ok(msg)) = ws_receiver.next().await {
            if msg.is_close() {
                break;
            }
        }
    });

    tokio::select! {
        _ = forward_task => {},
        _ = receive_task => {},
    }

    info!("Client {} disconnected", addr);
    clients.lock().await.retain(|(client_addr, _)| *client_addr != addr);
}