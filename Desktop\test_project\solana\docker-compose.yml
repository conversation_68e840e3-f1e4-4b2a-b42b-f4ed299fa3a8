version: '3.8'

services:
  pump-fun-monitor:
    build: .
    ports:
      - "8080:8080"
    environment:
      - RUST_LOG=info
      - SOLANA_RPC_WS_URL=wss://api.mainnet-beta.solana.com
      - SOLANA_RPC_HTTP_URL=https://api.mainnet-beta.solana.com
      - WEBSOCKET_PORT=8080
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - ./logs:/app/logs
    networks:
      - pump-monitor-network

  # reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - pump-fun-monitor
    networks:
      - pump-monitor-network
    profiles:
      - production

  # monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - pump-monitor-network
    profiles:
      - monitoring

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - pump-monitor-network
    profiles:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  pump-monitor-network:
    driver: bridge