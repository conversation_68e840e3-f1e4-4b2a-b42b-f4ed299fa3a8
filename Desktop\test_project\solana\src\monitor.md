use crate::config::Config;
use crate::error::{MonitorError, Result};
use crate::types::*;
use chrono::Utc;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use serde_json::json;
use solana_client::rpc_client::RpcClient;
use std::time::Duration;
use tokio::sync::broadcast;
use tokio::time::{interval, sleep};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use solana_rpc_client_api::config::RpcTransactionConfig;
use solana_client::rpc_client::GetConfirmedSignaturesForAddress2Config;
use solana_transaction_status::{EncodedTransactionWithStatusMeta, UiTransactionEncoding, option_serializer::OptionSerializer};
use solana_program::pubkey::Pubkey;
use spl_token::state::Mint;
use std::str::FromStr;

pub struct SolanaMonitor {
    config: Config,
    event_sender: broadcast::Sender<TokenCreationEvent>,
    rpc_client: RpcClient,
}

impl SolanaMonitor {
    pub fn new(config: Config, event_sender: broadcast::Sender<TokenCreationEvent>) -> Self {
        let rpc_client = RpcClient::new(&config.solana_rpc_http_url);
        
        Self {
            config,
            event_sender,
            rpc_client,
        }
    }

    pub async fn start(&self) -> Result<()> {
        info!("Starting Solana monitor for pump.fun contract: {}", self.config.pump_fun_program_id);
        
        loop {
            match self.connect_and_monitor().await {
                Ok(()) => {
                    warn!("Monitor connection closed, reconnecting in 5 seconds...");
                }
                Err(e) => {
                    error!("Monitor error: {}, reconnecting in 10 seconds...", e);
                    sleep(Duration::from_secs(10)).await;
                }
            }
            
            sleep(Duration::from_secs(5)).await;
        }
    }

    async fn connect_and_monitor(&self) -> Result<()> {
        info!("Connecting to Solana WebSocket: {}", self.config.solana_rpc_ws_url);
        
        let (ws_stream, _) = connect_async(&self.config.solana_rpc_ws_url).await?;
        let (mut write, mut read) = ws_stream.split();

        // Subscribe to program account changes
        let subscription_request = self.create_program_subscription()?;
        write.send(Message::Text(subscription_request)).await?;
        
        info!("Subscribed to pump.fun program account changes");

        // Start heartbeat to keep connection alive
        let mut heartbeat = interval(Duration::from_secs(30));
        
        loop {
            tokio::select! {
                message = read.next() => {
                    match message {
                        Some(Ok(Message::Text(text))) => {
                            if let Err(e) = self.handle_message(&text).await {
                                error!("Error handling message: {}", e);
                            }
                        }
                        Some(Ok(Message::Close(_))) => {
                            info!("WebSocket connection closed by server");
                            break;
                        }
                        Some(Err(e)) => {
                            error!("WebSocket error: {}", e);
                            break;
                        }
                        None => {
                            warn!("WebSocket stream ended");
                            break;
                        }
                        _ => {}
                    }
                }
                _ = heartbeat.tick() => {
                    // Send ping to keep connection alive
                    if let Err(e) = write.send(Message::Ping(vec![])).await {
                        error!("Failed to send ping: {}", e);
                        break;
                    }

                    // Generate test events for demonstration
                    //self.maybe_generate_test_event().await;
                }
            }
        }

        Ok(())
    }

    fn create_program_subscription(&self) -> Result<String> {
        let request = RpcRequest {
            jsonrpc: "2.0".to_string(),
            id: 1,
            method: "programSubscribe".to_string(),
            params: json!([
                self.config.pump_fun_program_id,
                {
                    "encoding": "base64",
                    "commitment": "confirmed"
                }
            ]),
        };

        Ok(serde_json::to_string(&request)?)
    }

    /// Handle incoming WebSocket messages
    async fn handle_message(&self, message: &str) -> Result<()> {
        debug!("Received message: {}", message);

        // Try to parse as subscription notification
        if let Ok(notification) = serde_json::from_str::<SubscriptionResponse>(message) {
            self.handle_subscription_notification(notification).await?;
        } else {
            // Handle other message types (subscription confirmations, errors, etc.)
            debug!("Non-notification message: {}", message);
        }

        Ok(())
    }

    /// Handle subscription notification from Solana
    async fn handle_subscription_notification(&self, notification: SubscriptionResponse) -> Result<()> {
        debug!("Processing subscription notification for slot: {}", notification.params.result.context.slot);

        // For program account changes, we need to fetch the transaction details
        // to determine if it's a token creation event
        let slot = notification.params.result.context.slot;
        
        // Get recent transactions for this program
        if let Ok(signatures) = self.get_program_signatures(slot).await {
            for signature in signatures {
                if let Ok(event) = self.parse_transaction_for_token_creation(&signature).await {
                    info!("Token creation event detected: {}", event.token.name);
                    
                    // Send event to WebSocket clients
                    if let Err(e) = self.event_sender.send(event) {
                        warn!("Failed to send event to clients: {}", e);
                    }
                }
            }
        }

        Ok(())
    }

    /// Get program signatures for a specific slot
    async fn get_program_signatures(&self, _slot: u64) -> Result<Vec<String>> {
        // Try to get real signatures from the blockchain
        match self.rpc_client.get_signatures_for_address_with_config(
            &self.config.pump_fun_program_id.parse().map_err(|e| MonitorError::ParseError(format!("Invalid program ID: {}", e)))?,
            GetConfirmedSignaturesForAddress2Config {
                limit: Some(5),
                commitment: Some(solana_sdk::commitment_config::CommitmentConfig::confirmed()),
                ..Default::default()
            },
        ) {
            Ok(signatures) => {
                let sigs: Vec<String> = signatures.into_iter().map(|sig| sig.signature).collect();
                if !sigs.is_empty() {
                    info!("Found {} recent signatures for pump.fun program", sigs.len());
                }
                Ok(sigs)
            }
            Err(e) => {
                warn!("Failed to get program signatures: {}", e);
                // For demonstration purposes, generate a test event periodically
                //self.maybe_generate_test_event().await;
                Ok(vec![])
            }
        }
    }



    /// START TEST CODE ONLY
    /// Generate a test token creation event for demonstration
    async fn maybe_generate_test_event(&self) {
        // Generate a test event every 10 seconds for demonstration
        static mut LAST_TEST_EVENT: std::sync::atomic::AtomicU64 = std::sync::atomic::AtomicU64::new(0);

        let now = chrono::Utc::now().timestamp() as u64;
        let last = unsafe { LAST_TEST_EVENT.load(std::sync::atomic::Ordering::Relaxed) };

        if now - last > 10 { // 10 seconds
            unsafe { LAST_TEST_EVENT.store(now, std::sync::atomic::Ordering::Relaxed) };

            let test_event = self.create_test_token_event().await;
            info!("Generated test token creation event: {}", test_event.token.name);

            if let Err(e) = self.event_sender.send(test_event) {
                warn!("Failed to send test event: {}", e);
            }
        }
    }

    /// Generate a Solana-like address from a seed
    fn generate_solana_address(&self, seed: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        seed.hash(&mut hasher);
        let hash = hasher.finish();

        // Generate a base58-like string (simplified)
        let chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
        let mut result = String::new();
        let mut num = hash;

        for _ in 0..44 { // Solana addresses are typically 44 characters
            result.push(chars.chars().nth((num % 58) as usize).unwrap());
            num /= 58;
            if num == 0 {
                num = hash.wrapping_mul(7); // Add some variation
            }
        }

        result
    }

    /// Create a realistic test token creation event
    async fn create_test_token_event(&self) -> TokenCreationEvent {
        let timestamp = chrono::Utc::now();
        let random_suffix = timestamp.timestamp() % 10000;

        let token_names = [
            "MoonCoin", "RocketToken", "DiamondHands", "ToTheMoon", "PumpMaster",
            "CryptoGem", "SolanaRocket", "DegenCoin", "LamboToken", "HodlCoin"
        ];
        let token_symbols = [
            "MOON", "RCKT", "DMND", "TTM", "PUMP",
            "GEM", "SLRKT", "DEGEN", "LAMBO", "HODL"
        ];

        let name_idx = (random_suffix as usize) % token_names.len();
        let symbol_idx = (random_suffix as usize) % token_symbols.len();

        // Generate realistic-looking Solana addresses
        let transaction_signature = self.generate_solana_address(&format!("tx_{}", random_suffix));
        let mint_address = self.generate_solana_address(&format!("mint_{}", random_suffix));
        let creator_address = self.generate_solana_address(&format!("creator_{}", random_suffix));
        let bonding_curve = self.generate_solana_address(&format!("curve_{}", random_suffix));

        TokenCreationEvent {
            event_type: "token_created".to_string(),
            timestamp,
            transaction_signature,
            token: TokenInfo {
                mint_address,
                name: token_names[name_idx].to_string(),
                symbol: token_symbols[symbol_idx].to_string(),
                creator: creator_address,
                supply: 1_000_000_000 + (random_suffix as u64 * 1_000_000),
                decimals: 6,
            },
            pump_data: PumpData {
                bonding_curve,
                virtual_sol_reserves: 30_000_000_000 + (random_suffix as u64 * 1_000_000),
                virtual_token_reserves: 1_073_000_000_000_000 + (random_suffix as u64 * 1_000_000_000),
            },
        }
    }

    /// START TEST CODE ONLY

    /// Parse transaction to check if it's a token creation event
    async fn parse_transaction_for_token_creation(&self, signature: &str) -> Result<TokenCreationEvent> {
        // Get transaction details
        let transaction = self.rpc_client.get_transaction_with_config(
            &signature.parse().map_err(|e| MonitorError::ParseError(format!("Invalid signature: {}", e)))?,
        RpcTransactionConfig {
                encoding: Some(UiTransactionEncoding::Json),
                commitment: Some(solana_sdk::commitment_config::CommitmentConfig::confirmed()),
                max_supported_transaction_version: Some(0),
            },
        )?;

        // Parse transaction logs to detect token creation
        if let Some(meta) = &transaction.transaction.meta {
            if let OptionSerializer::Some(log_messages) = &meta.log_messages {
                if self.is_token_creation_transaction(log_messages) {
                    return self.extract_token_info_from_transaction(&transaction.transaction, signature).await;
                }
            }
        }

        Err(MonitorError::ParseError("Not a token creation transaction".to_string()))
    }

    /// Check if transaction logs indicate a token creation
    fn is_token_creation_transaction(&self, logs: &[String]) -> bool {
        // Look for pump.fun specific patterns in transaction logs
        logs.iter().any(|log| {
            // Pump.fun specific log patterns for token creation
            log.contains("Program log: Instruction: Create") ||
            log.contains("Program log: create") ||
            log.contains("Program log: initialize") ||
            log.contains("InitializeMint") ||
            log.contains("CreateAssociatedTokenAccount") ||
            // Look for pump.fun program invocation
            log.contains(&self.config.pump_fun_program_id) ||
            // Look for token program interactions
            log.contains("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA") ||
            // Look for metadata creation
            log.contains("metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s")
        })
    }

    /// Extract token information from transaction
    async fn extract_token_info_from_transaction(
        &self,
        transaction: &EncodedTransactionWithStatusMeta,
        signature: &str,
    ) -> Result<TokenCreationEvent> {
        let mut mint_address = String::new();
        let mut creator = String::new();
        let mut token_name = String::new();
        let mut token_symbol = String::new();
        let mut bonding_curve = String::new();

        // Extract basic information from transaction
        // Since we're working with EncodedTransactionWithStatusMeta, we'll extract what we can
        // from the available fields and logs

        // Try to extract creator from transaction (this is a simplified approach)
        // In a real implementation, you'd parse the transaction signers
        // For now, we'll generate a fallback creator address

        // Extract from logs if available
        if let Some(meta) = &transaction.meta {
            if let OptionSerializer::Some(logs) = &meta.log_messages {
                for log in logs {
                    // Try to extract token info from logs
                    if log.contains("Program log:") {
                        // Parse pump.fun specific log data
                        if let Some(token_info) = self.parse_token_info_from_log(log) {
                            if !token_info.0.is_empty() {
                                token_name = token_info.0;
                            }
                            if !token_info.1.is_empty() {
                                token_symbol = token_info.1;
                            }
                        }
                    }

                    // Extract bonding curve address
                    if log.contains("bonding curve") || log.contains("curve") {
                        if let Some(curve_addr) = self.extract_address_from_log(log) {
                            bonding_curve = curve_addr;
                        }
                    }
                }
            }
        }

        // If we couldn't extract name/symbol from logs, generate from signature
        if token_name.is_empty() {
            token_name = format!("PumpToken_{}", &signature[..8]);
        }
        if token_symbol.is_empty() {
            token_symbol = format!("PUMP{}", &signature[..4].to_uppercase());
        }

        // If no mint address found, try to extract from account keys
        if mint_address.is_empty() {
            mint_address = self.find_mint_address_in_transaction(transaction).unwrap_or_else(|| {
                format!("MINT_{}", &signature[..8])
            });
        }

        // Get token supply and decimals from mint account if possible
        let (supply, decimals) = self.get_mint_info(&mint_address).await.unwrap_or((1_000_000_000, 6));

        let token_info = TokenInfo {
            mint_address,
            name: token_name,
            symbol: token_symbol,
            creator,
            supply,
            decimals,
        };

        // Extract pump.fun specific data
        let pump_data = self.extract_pump_data_from_transaction(transaction, &bonding_curve).await;

        Ok(TokenCreationEvent {
            event_type: "token_created".to_string(),
            timestamp: Utc::now(),
            transaction_signature: signature.to_string(),
            token: token_info,
            pump_data,
        })
    }
}

// Helper functions for parsing pump.fun specific data
impl SolanaMonitor {
    /// Parse token info from log message
    fn parse_token_info_from_log(&self, log: &str) -> Option<(String, String)> {
        // Try to extract token name and symbol from log messages
        // This is a heuristic approach since pump.fun logs may vary

        if log.contains("Program log:") {
            // Look for patterns like "name: TokenName" or "symbol: TKN"
            let mut name = String::new();
            let mut symbol = String::new();

            // Simple pattern matching for common log formats
            if let Some(name_start) = log.find("name:") {
                if let Some(name_part) = log[name_start + 5..].split_whitespace().next() {
                    name = name_part.trim_matches('"').to_string();
                }
            }

            if let Some(symbol_start) = log.find("symbol:") {
                if let Some(symbol_part) = log[symbol_start + 7..].split_whitespace().next() {
                    symbol = symbol_part.trim_matches('"').to_string();
                }
            }

            if !name.is_empty() || !symbol.is_empty() {
                return Some((name, symbol));
            }
        }

        None
    }

    /// Extract address from log message
    fn extract_address_from_log(&self, log: &str) -> Option<String> {
        // Look for Solana addresses (base58 encoded, typically 32-44 characters)
        let words: Vec<&str> = log.split_whitespace().collect();

        for word in words {
            // Check if this looks like a Solana address
            if word.len() >= 32 && word.len() <= 44 && word.chars().all(|c| c.is_alphanumeric()) {
                // Try to parse as Pubkey to validate
                if let Ok(_) = Pubkey::from_str(word) {
                    return Some(word.to_string());
                }
            }
        }

        None
    }

    /// Find mint address in transaction account keys
    fn find_mint_address_in_transaction(&self, transaction: &EncodedTransactionWithStatusMeta) -> Option<String> {
        // For now, we'll use a simplified approach to extract mint address
        // In a real implementation, you'd parse the transaction message properly

        // Try to extract from logs first
        if let Some(meta) = &transaction.meta {
            if let OptionSerializer::Some(logs) = &meta.log_messages {
                for log in logs {
                    if let Some(address) = self.extract_address_from_log(log) {
                        if !self.is_system_account(&address) {
                            return Some(address);
                        }
                    }
                }
            }
        }

        None
    }

    /// Check if an account is a system account
    fn is_system_account(&self, address: &str) -> bool {
        // Common system accounts to skip
        let system_accounts = [
            "11111111111111111111111111111111", // System Program
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", // Token Program
            "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL", // Associated Token Program
            "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s", // Metadata Program
        ];

        system_accounts.contains(&address)
    }

    /// Get mint info (supply and decimals) from blockchain
    async fn get_mint_info(&self, mint_address: &str) -> Option<(u64, u8)> {
        if let Ok(pubkey) = Pubkey::from_str(mint_address) {
            if let Ok(account) = self.rpc_client.get_account(&pubkey) {
                // Try to parse as SPL Token Mint
                if account.data.len() >= 82 { // Mint account size
                    // Parse mint data (simplified)
                    // In a real implementation, you'd use spl_token::state::Mint::unpack
                    let supply = u64::from_le_bytes([
                        account.data[36], account.data[37], account.data[38], account.data[39],
                        account.data[40], account.data[41], account.data[42], account.data[43],
                    ]);
                    let decimals = account.data[44];
                    return Some((supply, decimals));
                }
            }
        }
        None
    }

    /// Extract pump.fun specific data from transaction
    async fn extract_pump_data_from_transaction(
        &self,
        transaction: &EncodedTransactionWithStatusMeta,
        bonding_curve: &str,
    ) -> PumpData {
        let mut virtual_sol_reserves = 30_000_000_000u64; // Default values
        let mut virtual_token_reserves = 1_073_000_000_000_000u64;

        // Try to extract real values from transaction logs or account data
        if let Some(meta) = &transaction.meta {
            if let OptionSerializer::Some(logs) = &meta.log_messages {
                for log in logs {
                    // Look for reserve information in logs
                    if log.contains("sol_reserves") || log.contains("token_reserves") {
                        // Try to parse numeric values from logs
                        if let Some(sol_val) = self.extract_number_from_log(log, "sol_reserves") {
                            virtual_sol_reserves = sol_val;
                        }
                        if let Some(token_val) = self.extract_number_from_log(log, "token_reserves") {
                            virtual_token_reserves = token_val;
                        }
                    }
                }
            }
        }

        PumpData {
            bonding_curve: if bonding_curve.is_empty() {
                format!("CURVE_{}", chrono::Utc::now().timestamp())
            } else {
                bonding_curve.to_string()
            },
            virtual_sol_reserves,
            virtual_token_reserves,
        }
    }

    /// Extract numeric value from log message
    fn extract_number_from_log(&self, log: &str, field_name: &str) -> Option<u64> {
        if let Some(start) = log.find(field_name) {
            let after_field = &log[start + field_name.len()..];
            // Look for the first number after the field name
            for word in after_field.split_whitespace() {
                if let Ok(num) = word.trim_matches(|c: char| !c.is_numeric()).parse::<u64>() {
                    return Some(num);
                }
            }
        }
        None
    }
}