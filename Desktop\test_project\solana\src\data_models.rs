use borsh::BorshDeserialize;
use chrono::{DateTime, Utc};
use serde::Serialize;

/// The final, structured event broadcast to clients.
#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct TokenCreatedEvent {
    pub event_type: String,
    pub timestamp: DateTime<Utc>,
    pub transaction_signature: String,
    pub token: TokenDetails,
    pub pump_data: PumpFunData,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct TokenDetails {
    pub mint_address: String,
    pub name: String,
    pub symbol: String,
    pub uri: String,
    pub creator: String,
    pub supply: u64,
    pub decimals: u8,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct PumpFunData {
    pub global_account: String,
    pub bonding_curve: String,
    pub associated_bonding_curve: String,
}

/// Represents the arguments for the `pump.fun` `Create` instruction.
/// This is used for deserializing the instruction data with Borsh.
#[derive(BorshDeserialize, Debug)]
pub struct CreateInstructionData {
    pub name: String,
    pub symbol: String,
    pub uri: String,
}