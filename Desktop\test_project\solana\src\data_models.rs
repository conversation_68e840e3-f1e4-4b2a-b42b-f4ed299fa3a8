//! # Data Models
//!
//! This module defines the data structures used throughout the pump.fun monitor service.
//! All structures are designed to be serializable for JSON output and cloneable for
//! efficient broadcasting to multiple clients.

use borsh::BorshDeserialize;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// The main event structure broadcast to WebSocket clients when a new token is created.
///
/// This represents a complete token creation event from pump.fun, including all
/// relevant metadata and pump.fun-specific data. The structure follows camelCase
/// naming convention for JSON serialization to match web standards.
///
/// # Example JSON Output
/// ```json
/// {
///   "eventType": "tokenCreated",
///   "timestamp": "2024-01-15T10:30:45Z",
///   "transactionSignature": "5x7K8...",
///   "token": { ... },
///   "pumpData": { ... }
/// }
/// ```
#[derive(Serial<PERSON>, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenCreatedEvent {
    /// Event type identifier, always "tokenCreated" for token creation events
    pub event_type: String,
    /// UTC timestamp when the event was processed
    pub timestamp: DateTime<Utc>,
    /// Solana transaction signature containing the token creation
    pub transaction_signature: String,
    /// Detailed information about the created token
    pub token: TokenDetails,
    /// Pump.fun specific data including bonding curve information
    pub pump_data: PumpFunData,
}

/// Detailed information about a newly created token.
///
/// Contains all the standard SPL token metadata plus pump.fun specific information
/// like the creator address and initial supply configuration.
#[derive(Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenDetails {
    /// The mint address (public key) of the created token
    pub mint_address: String,
    /// Human-readable name of the token (e.g., "My Awesome Token")
    pub name: String,
    /// Token symbol/ticker (e.g., "MAT")
    pub symbol: String,
    /// URI pointing to token metadata JSON (following Metaplex standard)
    pub uri: String,
    /// Wallet address of the token creator (fee payer of the creation transaction)
    pub creator: String,
    /// Total supply of tokens created (in smallest unit, considering decimals)
    pub supply: u64,
    /// Number of decimal places for the token (typically 6 or 9)
    pub decimals: u8,
}

/// Pump.fun specific data extracted from the bonding curve and transaction.
///
/// This contains the economic parameters that define how the token's price
/// is determined on the pump.fun platform through its bonding curve mechanism.
#[derive(Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct PumpFunData {
    /// Address of the bonding curve account that manages token pricing
    pub bonding_curve: String,
    /// Virtual SOL reserves in the bonding curve (in lamports)
    pub virtual_sol_reserves: u64,
    /// Virtual token reserves in the bonding curve (in token's smallest unit)
    pub virtual_token_reserves: u64,
}


/// Raw bonding curve account data structure for Borsh deserialization.
///
/// This matches the on-chain data layout of pump.fun's bonding curve accounts.
/// Used to extract virtual reserve amounts from the account data.
#[derive(BorshDeserialize, Debug)]
pub struct BondingCurveAccountData {
    /// Virtual SOL reserves stored in the bonding curve
    pub virtual_sol_reserves: u64,
    /// Virtual token reserves stored in the bonding curve
    pub virtual_token_reserves: u64,
}

/// Instruction data for pump.fun's `Create` instruction.
///
/// This structure matches the arguments passed to the pump.fun program
/// when creating a new token. Used for deserializing instruction data
/// from transaction logs using Borsh serialization.
#[derive(BorshDeserialize, Debug)]
pub struct CreateInstructionData {
    /// The name of the token being created
    pub name: String,
    /// The symbol/ticker of the token being created
    pub symbol: String,
    /// URI pointing to the token's metadata JSON
    pub uri: String,
}


/// Client-side filtering criteria for token creation events.
///
/// This structure allows clients to specify which token creation events they want to receive.
/// All fields are optional, and filters are applied with AND logic (all specified criteria must match).
///
/// # Example JSON
/// ```json
/// {
///   "creator": "DEF456ghi789JKL012mno345PQR678stu901VWX234yza567BCD890efg123",
///   "symbol": "DOGE",
///   "nameContains": "moon"
/// }
/// ```
#[derive(Deserialize, Debug, Default, Clone)]
#[serde(rename_all = "camelCase")]
pub struct FilterCriteria {
    /// Exact match for token creator address (case-sensitive)
    pub creator: Option<String>,
    /// Exact match for token symbol (case-insensitive)
    pub symbol: Option<String>,
    /// Partial match for token name (case-insensitive)
    pub name_contains: Option<String>,
}

/// Messages that clients can send to the WebSocket server.
///
/// This enum defines the different types of control messages that clients can send
/// to configure their connection behavior. Currently supports filter configuration.
///
/// # Example JSON
/// ```json
/// {
///   "action": "setFilter",
///   "filter": {
///     "symbol": "DOGE",
///     "nameContains": "moon"
///   }
/// }
/// ```
#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase", tag = "action")]
pub enum ClientMessage {
    /// Set or update the client's event filter criteria
    SetFilter {
        /// The new filter criteria to apply
        filter: FilterCriteria
    },
}