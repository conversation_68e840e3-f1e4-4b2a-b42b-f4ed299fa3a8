use borsh::BorshDeserialize;
use chrono::{DateTime, Utc};
use serde::Serialize;

/// The final, structured event broadcast to clients.
#[derive(Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenCreatedEvent {
    pub event_type: String,
    pub timestamp: DateTime<Utc>,
    pub transaction_signature: String,
    pub token: TokenDetails,
    pub pump_data: PumpFunData,
}

#[derive(Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TokenDetails {
    pub mint_address: String,
    pub name: String,
    pub symbol: String,
    pub uri: String,
    pub creator: String,
    pub supply: u64,
    pub decimals: u8,
}

#[derive(Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct PumpFunData {
    pub bonding_curve: String,
    pub virtual_sol_reserves: u64,
    pub virtual_token_reserves: u64,
}


/// We use this to deserialize the account's raw data.
#[derive(BorshDeserialize, Debug)]
pub struct BondingCurveAccountData {
    pub virtual_sol_reserves: u64,
    pub virtual_token_reserves: u64,
}

/// This is used for deserializing the instruction data with Borsh.
#[derive(BorshDeserialize, Debug)]
pub struct CreateInstructionData {
    pub name: String,
    pub symbol: String,
    pub uri: String,
}