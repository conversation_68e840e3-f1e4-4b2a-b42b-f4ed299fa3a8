use crate::config::Config;
use crate::error::{MonitorError, Result};
use crate::types::*;
use chrono::Utc;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use serde_json::json;
use solana_client::rpc_client::RpcClient;
use std::collections::HashMap;
use std::time::Duration;
use tokio::sync::broadcast;
use tokio::time::{interval, sleep};
use tokio_tungstenite::{connect_async, tungstenite::Message};
// Add or modify these use statements at the top of src/monitor.rs
use solana_rpc_client_api::config::RpcTransactionConfig;
use solana_client::rpc_client::GetConfirmedSignaturesForAddress2Config;
use solana_transaction_status::{EncodedTransaction, EncodedTransactionWithStatusMeta, EncodedConfirmedTransactionWithStatusMeta, UiTransactionEncoding, option_serializer::OptionSerializer};

/// Monitors Solana blockchain for pump.fun token creation events
pub struct SolanaMonitor {
    config: Config,
    event_sender: broadcast::Sender<TokenCreationEvent>,
    rpc_client: RpcClient,
}

impl SolanaMonitor {
    pub fn new(config: Config, event_sender: broadcast::Sender<TokenCreationEvent>) -> Self {
        let rpc_client = RpcClient::new(&config.solana_rpc_http_url);
        
        Self {
            config,
            event_sender,
            rpc_client,
        }
    }

    /// Start monitoring for token creation events
    pub async fn start(&self) -> Result<()> {
        info!("Starting Solana monitor for pump.fun contract: {}", self.config.pump_fun_program_id);
        
        loop {
            match self.connect_and_monitor().await {
                Ok(()) => {
                    warn!("Monitor connection closed, reconnecting in 5 seconds...");
                }
                Err(e) => {
                    error!("Monitor error: {}, reconnecting in 10 seconds...", e);
                    sleep(Duration::from_secs(10)).await;
                }
            }
            
            sleep(Duration::from_secs(5)).await;
        }
    }

    /// Connect to Solana WebSocket and start monitoring
    async fn connect_and_monitor(&self) -> Result<()> {
        info!("Connecting to Solana WebSocket: {}", self.config.solana_rpc_ws_url);
        
        let (ws_stream, _) = connect_async(&self.config.solana_rpc_ws_url).await?;
        let (mut write, mut read) = ws_stream.split();

        // Subscribe to program account changes
        let subscription_request = self.create_program_subscription()?;
        write.send(Message::Text(subscription_request)).await?;
        
        info!("Subscribed to pump.fun program account changes");

        // Start heartbeat to keep connection alive
        let mut heartbeat = interval(Duration::from_secs(30));
        
        loop {
            tokio::select! {
                message = read.next() => {
                    match message {
                        Some(Ok(Message::Text(text))) => {
                            if let Err(e) = self.handle_message(&text).await {
                                error!("Error handling message: {}", e);
                            }
                        }
                        Some(Ok(Message::Close(_))) => {
                            info!("WebSocket connection closed by server");
                            break;
                        }
                        Some(Err(e)) => {
                            error!("WebSocket error: {}", e);
                            break;
                        }
                        None => {
                            warn!("WebSocket stream ended");
                            break;
                        }
                        _ => {}
                    }
                }
                _ = heartbeat.tick() => {
                    // Send ping to keep connection alive
                    if let Err(e) = write.send(Message::Ping(vec![])).await {
                        error!("Failed to send ping: {}", e);
                        break;
                    }
                }
            }
        }

        Ok(())
    }

    /// Create subscription request for program account changes
    fn create_program_subscription(&self) -> Result<String> {
        let request = RpcRequest {
            jsonrpc: "2.0".to_string(),
            id: 1,
            method: "programSubscribe".to_string(),
            params: json!([
                self.config.pump_fun_program_id,
                {
                    "encoding": "base64",
                    "commitment": "confirmed"
                }
            ]),
        };

        Ok(serde_json::to_string(&request)?)
    }

    /// Handle incoming WebSocket messages
    async fn handle_message(&self, message: &str) -> Result<()> {
        debug!("Received message: {}", message);

        // Try to parse as subscription notification
        if let Ok(notification) = serde_json::from_str::<SubscriptionResponse>(message) {
            self.handle_subscription_notification(notification).await?;
        } else {
            // Handle other message types (subscription confirmations, errors, etc.)
            debug!("Non-notification message: {}", message);
        }

        Ok(())
    }

    /// Handle subscription notification from Solana
    async fn handle_subscription_notification(&self, notification: SubscriptionResponse) -> Result<()> {
        debug!("Processing subscription notification for slot: {}", notification.params.result.context.slot);

        // For program account changes, we need to fetch the transaction details
        // to determine if it's a token creation event
        let slot = notification.params.result.context.slot;
        
        // Get recent transactions for this program
        if let Ok(signatures) = self.get_program_signatures(slot).await {
            for signature in signatures {
                if let Ok(event) = self.parse_transaction_for_token_creation(&signature).await {
                    info!("Token creation event detected: {}", event.token.name);
                    
                    // Send event to WebSocket clients
                    if let Err(e) = self.event_sender.send(event) {
                        warn!("Failed to send event to clients: {}", e);
                    }
                }
            }
        }

        Ok(())
    }

    /// Get program signatures for a specific slot
    async fn get_program_signatures(&self, _slot: u64) -> Result<Vec<String>> {
        // In a real implementation, we would fetch signatures for the specific slot
        // For now, we'll get recent signatures for the program
        match self.rpc_client.get_signatures_for_address_with_config(
            &self.config.pump_fun_program_id.parse().map_err(|e| MonitorError::ParseError(format!("Invalid program ID: {}", e)))?,
            GetConfirmedSignaturesForAddress2Config {
                limit: Some(10),
                commitment: Some(solana_sdk::commitment_config::CommitmentConfig::confirmed()),
                ..Default::default()
            },
        ) {
            Ok(signatures) => {
                Ok(signatures.into_iter().map(|sig| sig.signature).collect())
            }
            Err(e) => {
                warn!("Failed to get program signatures: {}", e);
                Ok(vec![])
            }
        }
    }

    /// Parse transaction to check if it's a token creation event
    async fn parse_transaction_for_token_creation(&self, signature: &str) -> Result<TokenCreationEvent> {
        // Get transaction details
        let transaction = self.rpc_client.get_transaction_with_config(
            &signature.parse().map_err(|e| MonitorError::ParseError(format!("Invalid signature: {}", e)))?,
        RpcTransactionConfig {
                encoding: Some(UiTransactionEncoding::Json),
                commitment: Some(solana_sdk::commitment_config::CommitmentConfig::confirmed()),
                max_supported_transaction_version: Some(0),
            },
        )?;

        // Parse transaction logs to detect token creation
        if let Some(meta) = &transaction.transaction.meta {
            if let OptionSerializer::Some(log_messages) = &meta.log_messages {
                if self.is_token_creation_transaction(log_messages) {
                    return self.extract_token_info_from_transaction(&transaction.transaction, signature).await;
                }
            }
        }

        Err(MonitorError::ParseError("Not a token creation transaction".to_string()))
    }

    /// Check if transaction logs indicate a token creation
    fn is_token_creation_transaction(&self, logs: &[String]) -> bool {
        // Look for patterns in logs that indicate token creation
        // This is a simplified implementation - in practice, you'd need to
        // understand the specific program instructions and logs
        logs.iter().any(|log| {
            log.contains("Program log: Instruction: Create") ||
            log.contains("Program log: InitializeMint") ||
            log.contains("mint") && log.contains("create")
        })
    }

    /// Extract token information from transaction
    async fn extract_token_info_from_transaction(
        &self,
        transaction: &EncodedTransaction,
        signature: &str,
    ) -> Result<TokenCreationEvent> {
        // This is a simplified token info extraction
        // In practice, you'd need to decode the program instruction data
        // and parse the specific pump.fun program state
        
        let token_info = TokenInfo {
            mint_address: "".to_string(), // Would extract from instruction data
            name: format!("Token_{}", &signature[..8]), // Placeholder
            symbol: format!("TKN_{}", &signature[..4]), // Placeholder
            creator: "".to_string(), // Would extract from transaction accounts
            supply: 1_000_000_000, // Default supply
            decimals: 6,
        };

        let pump_data = PumpData {
            bonding_curve: "".to_string(), // Would extract from program state
            virtual_sol_reserves: 30_000_000_000,
            virtual_token_reserves: 1_073_000_000_000_000,
        };

        Ok(TokenCreationEvent {
            event_type: "token_created".to_string(),
            timestamp: Utc::now(),
            transaction_signature: signature.to_string(),
            token: token_info,
            pump_data,
        })
    }
}

// Helper functions for parsing pump.fun specific data
impl SolanaMonitor {
    /// Parse pump.fun program instruction data
    fn parse_pump_instruction_data(&self, _data: &[u8]) -> Result<HashMap<String, String>> {
        // This would contain the actual parsing logic for pump.fun instructions
        // The instruction data format would need to be reverse-engineered or
        // obtained from pump.fun documentation
        Ok(HashMap::new())
    }

    /// Extract mint address from accounts
    fn extract_mint_address(&self, _accounts: &[String]) -> Option<String> {
        // Parse account keys to find the mint account
        None
    }

    /// Get token metadata from mint address
    async fn get_token_metadata(&self, _mint: &str) -> Result<TokenInfo> {
        // Fetch token metadata from the blockchain
        Ok(TokenInfo::default())
    }
}