#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Config {
    pub websocket_port: u16,
    
    pub solana_rpc_ws_url: String,
    
    pub solana_rpc_http_url: String,
    
    pub pump_fun_program_id: String,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            websocket_port: 8080,
            solana_rpc_ws_url: "wss://api.mainnet-beta.solana.com".to_string(),
            solana_rpc_http_url: "https://api.mainnet-beta.solana.com".to_string(),
            pump_fun_program_id: "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string(),
        }
    }
}